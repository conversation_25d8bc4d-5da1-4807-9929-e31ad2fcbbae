# StarRocks 物化视图自动生成器

这个工具可以帮助您自动化管理 StarRocks 的物化视图，特别是在添加新指标时自动在所有 UNION ALL 子查询中补 0，避免手动修改每个子查询。

## 功能特点

- ✅ **自动补 0**: 添加新指标时，自动在不相关的子查询中补 0
- ✅ **配置化管理**: 通过 JSON 文件管理指标定义
- ✅ **类型安全**: 使用 Python 数据类确保配置正确性
- ✅ **可扩展**: 轻松添加新的子查询和指标
- ✅ **代码生成**: 自动生成完整的 SQL 物化视图定义

## 快速开始

### 1. 生成基础文件

```bash
python3 generate_materialized_view.py
```

这会生成：

- `generated_view_application_kpi.sql` - 基础 SQL 文件
- `view_config.json` - 配置文件

### 2. 添加新指标（方式一：程序化）

```python
from generate_materialized_view import MaterializedViewGenerator, MetricDefinition

generator = MaterializedViewGenerator()

# 添加新指标
new_metric = MetricDefinition(
    name="custom_metric",
    expression="COUNT(DISTINCT CASE WHEN condition = 1 THEN id END)",
    applicable_unions=["submit_to_job", "offer"]  # 只在这些子查询中计算
)

generator.add_metric(new_metric)
generator.save_to_file("updated_view.sql")
```

### 3. 添加新指标（方式二：配置文件）

编辑 `view_config.json`，添加新指标：

```json
{
  "metrics": [
    // ... 现有指标 ...
    {
      "name": "new_custom_metric",
      "expression": "COUNT(DISTINCT custom_id)",
      "condition": "",
      "applicable_unions": ["submit_to_job"]
    }
  ]
}
```

然后重新生成：

```python
generator = MaterializedViewGenerator()
generator.load_config_from_json("view_config.json")
generator.save_to_file("updated_view.sql")
```

## 示例

查看 `example_usage.py` 获取完整的使用示例：

```bash
python3 example_usage.py
```

## 配置说明

### MetricDefinition 字段

- `name`: 指标名称
- `expression`: SQL 表达式
- `condition`: 额外条件（可选）
- `applicable_unions`: 适用的子查询列表

### 子查询类型

当前支持的子查询类型：

- `submit_to_job` - 提交到职位
- `submit_to_client` - 提交到客户
- `interview` - 面试
- `reserve_interview` - 预约面试
- `offer` - 发 offer
- `offer_accept` - 接受 offer
- `onboard` - 入职
- `eliminate` - 淘汰

## 工作流程

1. **定义指标**: 在配置文件中定义新指标
2. **指定适用范围**: 确定指标在哪些子查询中计算
3. **自动生成**: 脚本自动在其他子查询中补 0
4. **生成 SQL**: 输出完整的物化视图 SQL

## 生成的文件

- `generated_view_application_kpi.sql` - 基础物化视图
- `view_config.json` - 指标配置文件
- `updated_view_application_kpi.sql` - 添加新指标后的视图
- `config_based_view.sql` - 基于配置文件生成的视图

## 注意事项

- 新指标会自动在所有子查询中定义，不相关的子查询会自动补 0
- 修改配置文件后需要重新运行脚本生成 SQL
- 生成的 SQL 保持与原始视图相同的结构和性能特性
