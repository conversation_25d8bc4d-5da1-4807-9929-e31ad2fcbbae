#!/usr/bin/env python3
"""
StarRocks 物化视图生成器
用于自动化管理 view_application_kpi 物化视图，支持添加新指标时自动在所有 UNION ALL 子查询中补0
"""

from typing import List
from dataclasses import dataclass


@dataclass
class MetricDefinition:
    """指标定义"""

    name: str
    expression: str  # 计算表达式，如 "COUNT(DISTINCT submit_job_id)"
    applicable_unions: List[str] = None  # 适用的子查询，如果为None则所有子查询都补0

    def __post_init__(self):
        if self.applicable_unions is None:
            self.applicable_unions = []


class MaterializedViewGenerator:
    """物化视图生成器"""

    def __init__(self):
        # 基础字段 - 这些在所有子查询中都是相同的
        self.base_fields = [
            "tenant_id",
            "company_id",
            "job_id",
            "user_id",
            "user_name",
            "user_role",
            "team_id",
            "team_name",
        ]

        # 子查询名称列表
        self.union_names = [
            "submit_to_job",
            "submit_to_client",
            "interview",
            "reserve_interview",
            "offer",
            "offer_accept",
            "onboard",
            "eliminate",
        ]

        # 所有指标定义
        self.metrics: List[MetricDefinition] = []

        self._init_default_metrics()

    def _init_default_metrics(self):
        """初始化默认指标"""
        self.metrics = [
            # submit_to_job 相关指标
            MetricDefinition(
                name="submit_to_job_currentCountNum",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = 10) THEN submit_job_id END)",
                applicable_unions=["submit_to_job"],
            ),
            MetricDefinition(
                name="submit_to_job_countNum",
                expression="COUNT(DISTINCT submit_job_id)",
                applicable_unions=["submit_to_job"],
            ),
            MetricDefinition(
                name="submit_to_job_currentStayedOver",
                expression="COUNT(DISTINCT IF(node_status = 1 AND node_type = 10 AND TIMESTAMPDIFF(HOUR, submit_job_last_modify_date, NOW()) > 24, submit_job_id, NULL))",
                applicable_unions=["submit_to_job"],
            ),
            MetricDefinition(
                name="submit_to_job_stayedOver",
                expression="COUNT(DISTINCT IF(TIMESTAMPDIFF(HOUR, submit_job_last_modify_date, NOW()) > 24, submit_job_id, NULL))",
                applicable_unions=["submit_to_job"],
            ),
            # submit_to_client 相关指标
            MetricDefinition(
                name="submit_to_client_currentCountNum",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = 20) THEN submit_client_id END)",
                applicable_unions=["submit_to_client"],
            ),
            MetricDefinition(
                name="submit_to_client_countNum",
                expression="COUNT(DISTINCT submit_client_id)",
                applicable_unions=["submit_to_client"],
            ),
            MetricDefinition(
                name="submit_to_client_currentStayedOver",
                expression="COUNT(DISTINCT IF(node_status = 1 AND node_type = 10 AND TIMESTAMPDIFF(HOUR, submit_client_last_modified_date, NOW()) > 24, submit_client_id, NULL))",
                applicable_unions=["submit_to_client"],
            ),
            MetricDefinition(
                name="submit_to_client_stayedOver",
                expression="COUNT(DISTINCT IF(TIMESTAMPDIFF(HOUR, submit_client_last_modified_date, NOW()) > 24, submit_client_id, NULL))",
                applicable_unions=["submit_to_client"],
            ),
            # offer 相关指标
            MetricDefinition(
                name="offer_currentCountNum",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = 40) THEN offer_id END)",
                applicable_unions=["offer"],
            ),
            MetricDefinition(
                name="offer_countNum",
                expression="COUNT(DISTINCT offer_id)",
                applicable_unions=["offer"],
            ),
            MetricDefinition(
                name="offer_accept_currentCountNum",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = 41) THEN offer_accept_id END)",
                applicable_unions=["offer_accept"],
            ),
            MetricDefinition(
                name="offer_accept_countNum",
                expression="COUNT(DISTINCT offer_accept_id)",
                applicable_unions=["offer_accept"],
            ),
            # onboard 相关指标
            MetricDefinition(
                name="on_board_currentCountNum",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = 60) THEN onboard_id END)",
                applicable_unions=["onboard"],
            ),
            MetricDefinition(
                name="on_board_countNum",
                expression="COUNT(DISTINCT onboard_id)",
                applicable_unions=["onboard"],
            ),
            # eliminated 相关指标
            MetricDefinition(
                name="eliminated_currentCountNum",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = -1) THEN eliminate_id END)",
                applicable_unions=["eliminate"],
            ),
            MetricDefinition(
                name="eliminated_countNum",
                expression="COUNT(DISTINCT eliminate_id)",
                applicable_unions=["eliminate"],
            ),
            # interview 相关指标
            MetricDefinition(
                name="interview1",
                expression="COUNT(DISTINCT CASE WHEN interview_progress = 1 THEN interview_id END)",
                applicable_unions=["interview", "reserve_interview"],
            ),
            MetricDefinition(
                name="interview2",
                expression="COUNT(DISTINCT CASE WHEN interview_progress = 2 THEN interview_id END)",
                applicable_unions=["interview", "reserve_interview"],
            ),
            MetricDefinition(
                name="current_interview1",
                expression="COUNT(DISTINCT CASE WHEN (interview_progress = 1 AND node_status = 1 AND node_type = 30 AND max_progress_subquery.progress = 1) THEN interview_id END)",
                applicable_unions=["interview"],
            ),
            MetricDefinition(
                name="current_interview2",
                expression="COUNT(DISTINCT CASE WHEN (interview_progress = 2 AND node_status = 1 AND node_type = 30 AND max_progress_subquery.progress = 2) THEN interview_id END)",
                applicable_unions=["interview"],
            ),
            MetricDefinition(
                name="two_or_more_interviews",
                expression="COUNT(DISTINCT CASE WHEN (interview_progress > 2 AND interview_final_round <> 1) THEN interview_id END)",
                applicable_unions=["interview", "reserve_interview"],
            ),
            MetricDefinition(
                name="current_two_or_more_interviews",
                expression="COUNT(DISTINCT CASE WHEN (interview_progress > 2 AND interview_final_round <> 1 AND node_status = 1 AND node_type = 30) THEN interview_id END)",
                applicable_unions=["interview"],
            ),
            MetricDefinition(
                name="interview_final",
                expression="COUNT(DISTINCT CASE WHEN interview_final_round = 1 THEN interview_id END)",
                applicable_unions=["interview", "reserve_interview"],
            ),
            MetricDefinition(
                name="current_interview_final",
                expression="COUNT(DISTINCT CASE WHEN (interview_final_round = 1 AND node_status = 1 AND node_type = 30) THEN interview_id END)",
                applicable_unions=["interview"],
            ),
            MetricDefinition(
                name="interview_total",
                expression="COUNT(DISTINCT interview_id)",
                applicable_unions=["interview", "reserve_interview"],
            ),
            MetricDefinition(
                name="current_interview_total",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = 30) THEN interview_id END)",
                applicable_unions=["interview", "reserve_interview"],
            ),
            MetricDefinition(
                name="unique_interview_talents",
                expression="0",  # 这个指标在原SQL中始终为0
                applicable_unions=[],
            ),
            MetricDefinition(
                name="reserve_interview_total",
                expression="COUNT(DISTINCT interview_id)",
                applicable_unions=["reserve_interview"],
            ),
            MetricDefinition(
                name="reserve_current_interview_total",
                expression="COUNT(DISTINCT CASE WHEN (node_status = 1 AND node_type = 30) THEN interview_id END)",
                applicable_unions=["reserve_interview"],
            ),
        ]

    def add_metric(self, metric: MetricDefinition):
        """添加新指标"""
        self.metrics.append(metric)

    def generate_select_fields(self) -> str:
        """生成SELECT字段部分"""
        select_fields = []

        # 基础字段
        for field in self.base_fields:
            select_fields.append(f"       unified_data.{field},")

        select_fields.append(f"       unified_data.add_date,")
        select_fields.append(f"       unified_data.event_date,")
        select_fields.append("")  # 空行分隔

        # 指标字段
        for metric in self.metrics:
            select_fields.append(f"       {metric.name:30} AS {metric.name},")

        # 移除最后一个逗号
        if select_fields and select_fields[-1].endswith(","):
            select_fields[-1] = select_fields[-1][:-1]

        return "\n".join(select_fields)

    def generate_union_subquery(self, union_name: str) -> str:
        """生成单个UNION子查询"""
        lines = []
        lines.append(f"      (SELECT {', '.join(self.base_fields)},")

        # 根据子查询确定日期字段映射
        if union_name == "submit_to_job":
            lines.append(f"              submit_job_created_date       AS add_date,")
            lines.append(f"              submit_job_created_date       AS event_date,")
        elif union_name == "submit_to_client":
            lines.append(f"              submit_client_created_date       AS add_date,")
            lines.append(
                f"              submit_client_time               AS event_date,"
            )
        elif union_name == "interview":
            lines.append(f"              interview_created_date       AS add_date,")
            lines.append(f"              interview_from_time_utc      AS event_date,")
        elif union_name == "reserve_interview":
            lines.append(f"              interview_created_date       AS add_date,")
            lines.append(f"              interview_created_date       AS event_date,")
        elif union_name == "offer":
            lines.append(f"              offer_created_date       AS add_date,")
            lines.append(f"              offer_created_date       AS event_date,")
        elif union_name == "offer_accept":
            lines.append(f"              offer_accept_created_date       AS add_date,")
            lines.append(
                f"              offer_accept_created_date       AS event_date,"
            )
        elif union_name == "onboard":
            lines.append(f"              onboard_node_created_date  AS add_date,")
            lines.append(f"              onboard_date               AS event_date,")
        elif union_name == "eliminate":
            lines.append(f"              eliminate_created_date       AS add_date,")
            lines.append(f"              eliminate_created_date       AS event_date,")

        # 生成指标字段
        for metric in self.metrics:
            if union_name in metric.applicable_unions:
                lines.append(f"              {metric.expression:50} AS {metric.name},")
            else:
                lines.append(f"              {'0':50} AS {metric.name},")

        # 移除最后一个逗号
        if lines and lines[-1].endswith(","):
            lines[-1] = lines[-1][:-1]

        lines.append(f"       FROM view_application AS application")

        # 添加特殊的JOIN（仅interview需要）
        if union_name == "interview":
            lines.append(
                f"                LEFT OUTER JOIN (SELECT application.talent_recruitment_process_id AS application_id, max(interview_progress) AS progress FROM view_application AS application GROUP BY application_id) AS max_progress_subquery ON application_id = talent_recruitment_process_id"
            )

        # 添加GROUP BY
        group_fields = self.base_fields + ["add_date", "event_date"]
        lines.append(f"       GROUP BY {', '.join(group_fields)})")

        return "\n".join(lines)

    def generate_materialized_view(
        self, view_name: str = "ods_apn.view_application_kpi"
    ) -> str:
        """生成完整的物化视图SQL"""
        lines = []

        # 视图定义头部
        lines.append("CREATE")
        lines.append(f"MATERIALIZED VIEW IF NOT EXISTS {view_name}")
        lines.append("       DISTRIBUTED BY HASH (`user_id`)")
        lines.append("REFRESH")
        lines.append("       ASYNC EVERY (INTERVAL 10 MINUTE)")
        lines.append("PROPERTIES ( 'session.enable_spill'='true' )")
        lines.append("AS")

        # SELECT字段
        lines.append("SELECT " + self.generate_select_fields().lstrip())
        lines.append("")
        lines.append("FROM ((")

        # 生成所有UNION子查询
        union_parts = []
        for union_name in self.union_names:
            union_parts.append(self.generate_union_subquery(union_name))

        # 用UNION ALL连接
        lines.append("\n      UNION ALL\n      ".join(union_parts))

        lines.append("                event_date)) AS unified_data;")

        return "\n".join(lines)

    def save_to_file(
        self, filename: str, view_name: str = "ods_apn.view_application_kpi"
    ):
        """保存生成的SQL到文件"""
        sql_content = self.generate_materialized_view(view_name)
        with open(filename, "w", encoding="utf-8") as f:
            f.write(sql_content)
        print(f"物化视图SQL已保存到: {filename}")


def main():
    """主函数"""
    generator = MaterializedViewGenerator()

    # 在这里添加您的自定义指标
    # 示例：
    # custom_metric = MetricDefinition(
    #     name="custom_metric_name",
    #     expression="COUNT(DISTINCT CASE WHEN condition = 1 THEN some_id END)",
    #     applicable_unions=["submit_to_job", "offer"]  # 只在这些子查询中计算
    # )
    # generator.add_metric(custom_metric)

    # 生成SQL文件
    generator.save_to_file("view_application_kpi.sql")
    print("✅ 生成完成！")


if __name__ == "__main__":
    main()
