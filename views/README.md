# StarRocks 物化视图生成器

简洁的工具，用于自动化管理 StarRocks 物化视图，在添加新指标时自动在所有 UNION ALL 子查询中补 0。

## 核心功能

- ✅ **自动补 0**：新指标只在指定子查询中计算，其他自动补 0
- ✅ **代码配置**：通过 Python 代码定义指标
- ✅ **一键生成**：自动生成完整的物化视图 SQL

## 使用方法

### 1. 基础生成

```bash
python3 generate_materialized_view.py
```

生成 `view_application_kpi.sql` 文件

### 2. 添加自定义指标

编辑 `generate_materialized_view.py` 的 `main()` 函数：

```python
def main():
    generator = MaterializedViewGenerator()

    # 添加自定义指标
    custom_metric = MetricDefinition(
        name="custom_metric_name",
        expression="COUNT(DISTINCT CASE WHEN condition = 1 THEN some_id END)",
        applicable_unions=["submit_to_job", "offer"]  # 只在这些子查询中计算
    )
    generator.add_metric(custom_metric)

    # 生成SQL
    generator.save_to_file("view_application_kpi.sql")
    print("✅ 生成完成！")
```

### 3. 支持的子查询类型

- `submit_to_job` - 提交到职位
- `submit_to_client` - 提交到客户
- `interview` - 面试
- `reserve_interview` - 预约面试
- `offer` - 发 offer
- `offer_accept` - 接受 offer
- `onboard` - 入职
- `eliminate` - 淘汰

## 工作原理

1. 在 `main()` 函数中定义新指标
2. 指定指标适用的子查询列表
3. 脚本自动在其他子查询中补 0
4. 生成完整的物化视图 SQL

## 示例

```python
# 添加一个只在 submit_to_job 中计算的指标
new_metric = MetricDefinition(
    name="urgent_submit_count",
    expression="COUNT(DISTINCT CASE WHEN priority = 'URGENT' THEN submit_job_id END)",
    applicable_unions=["submit_to_job"]
)
generator.add_metric(new_metric)
```

这将在 `submit_to_job` 子查询中添加实际计算，在其他 7 个子查询中自动补 0。
