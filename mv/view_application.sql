CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.view_application
       DISTRIBUTED BY HASH (`user_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
PROPERTIES ( 'session.enable_spill'='true' )
AS
SELECT pt.id                                                                        AS team_id,
       pt.name                                                                      AS team_name,
       u.id                                                                         AS user_id,
       ul.user_role                                                                 AS user_role,
       CONCAT(u.first_name, ' ', u.last_name)                                       AS user_name,
       c.id                                                                         AS company_id,
       j.recruitment_process_id                                                     AS recruitment_process_id,

       rf.id                                                                        AS recommend_feedback_id,

       trp.id                                                                       AS talent_recruitment_process_id,
       trp.job_id                                                                   AS job_id,
       trp.talent_id                                                                AS talent_id,
       trp.tenant_id                                                                AS tenant_id,
       trp.ai_score                                                                 AS ai_score,
       trp.created_date                                                             AS talent_recruitment_process_created_date,
       trp.last_modified_date                                                       AS talent_recruitment_process_last_modified_date,
       trp.note                                                                     AS current_note,

       trpn.id                                                                      AS node_id,
       trpn.node_type                                                               AS node_type,
       trpn.node_status                                                             AS node_status,
       trpn.created_date                                                            AS node_created_date,
       trpn.last_modified_date                                                      AS node_last_modified_date,

       submit_job.id                                                                AS submit_job_id,
       submit_job.note                                                              AS sunmit_job_note,
       submit_job.created_date                                                      AS submit_job_created_date,
       submit_job.last_modified_date                                                AS submit_job_last_modify_date,

       submit_client.id                                                             AS submit_client_id,
       submit_client.submit_time                                                    AS submit_client_time,
       submit_client.note                                                           AS submit_client_note,
       submit_client.created_date                                                   AS submit_client_created_date,
       submit_client.last_modified_date                                             AS submit_client_last_modified_date,

       interview.id                                                                 AS interview_id,
       interview.progress                                                           AS interview_progress,
       interview.from_time                                                          AS interview_form_time,
       CONVERT_TZ(interview.from_time, COALESCE(interview.time_zone, 'UTC'), 'UTC') AS interview_from_time_utc,
       interview.to_time                                                            AS interview_to_time,
       CONVERT_TZ(interview.to_time, COALESCE(interview.time_zone, 'UTC'), 'UTC')   AS interview_from_to_utc,
       interview.final_round                                                        AS interview_final_round,
       interview.note                                                               AS interview_note,
       interview.created_date                                                       AS interview_created_date,
       interview.last_modified_date                                                 AS interview_last_modified_date,

       offer.id                                                                     AS offer_id,
       offer.note                                                                   AS offer_note,
       offer.created_date                                                           AS offer_created_date,
       offer.last_modified_date                                                     AS offer_last_modified_date,

       offer_accept.id                                                              AS offer_accept_id,
       offer_accept.note                                                            AS offer_accept_note,
       offer_accept.created_date                                                    AS offer_accept_created_date,
       offer_accept.last_modified_date                                              AS offer_accept_last_modified_date,

       onboard.id                                                                   AS onboard_id,
       onboard.created_date                                                         AS onboard_node_created_date,
       onboard.last_modified_date                                                   AS onboard_node_last_modified_date,
       trpod.onboard_date                                                           AS onboard_date,
       onboard.note                                                                 AS onboard_note,

       resignation.id                                                               AS resignation_id,
       resignation.created_date                                                     AS resignation_created_date,
       resignation.last_modified_date                                               AS resignation_last_modified_date,
       resignation.resign_date                                                      AS resignation_resign_date,
       resignation.reason                                                           AS resignation_reason,

       eliminate.id                                                                 AS eliminate_id,
       eliminate.note                                                               AS eliminate_note,
       eliminate.created_date                                                       AS eliminate_created_date,
       eliminate.last_modified_date                                                 AS eliminate_last_modified_date

FROM talent_recruitment_process trp
         INNER JOIN recruitment_process rp ON trp.recruitment_process_id = rp.id
         INNER JOIN job j on trp.job_id = j.id
         INNER JOIN company c ON j.company_id = c.id
         INNER JOIN talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id
         INNER JOIN user u ON u.id = ul.user_id
         INNER JOIN permission_user_team put ON put.user_id = u.id AND put.is_primary = 1
         INNER JOIN permission_team pt ON pt.id = put.team_id

         LEFT JOIN job_talent_recommend_feedback rf on rf.job_id = trp.job_id and rf.talent_id = trp.talent_id and
                                                       rf.reason in ('ADD_TO_POSITION', 'ADD_TO_ASSOCIATION_JOB_FOLDER')

         LEFT JOIN talent_recruitment_process_node trpn ON trpn.talent_recruitment_process_id = trp.id
         LEFT JOIN talent_recruitment_process_onboard onboard ON onboard.talent_recruitment_process_id = trp.id

         LEFT JOIN talent_recruitment_process_onboard_date trpod ON trp.id = trpod.talent_recruitment_process_id

         LEFT JOIN talent_recruitment_process_submit_to_job submit_job
                   ON submit_job.talent_recruitment_process_id = trp.id

         LEFT JOIN talent_recruitment_process_submit_to_client submit_client
                   ON submit_client.talent_recruitment_process_id = trp.id

         LEFT JOIN talent_recruitment_process_offer offer ON offer.talent_recruitment_process_id = trp.id

         LEFT JOIN talent_recruitment_process_ipg_offer_accept offer_accept
                   ON offer_accept.talent_recruitment_process_id = trp.id

         LEFT JOIN talent_recruitment_process_eliminate eliminate ON eliminate.talent_recruitment_process_id = trp.id

         LEFT JOIN talent_recruitment_process_interview interview ON interview.talent_recruitment_process_id = trp.id
         LEFT JOIN talent_recruitment_process_note note ON note.talent_recruitment_process_id = trp.id
         LEFT JOIN talent_recruitment_process_resignation resignation
                   ON resignation.talent_recruitment_process_id = trp.id;
