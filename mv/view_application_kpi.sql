CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.view_application_kpi
       DISTRIBUTED BY HASH (`user_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
PROPERTIES ( 'session.enable_spill'='true' )
AS
SELECT unified_data.tenant_id,
       unified_data.company_id,
       unified_data.job_id,
       unified_data.user_name,
       unified_data.team_id,
       unified_data.team_name,
       unified_data.add_date,
       unified_data.event_date,

       submit_to_job_currentCountNum      AS submit_to_job_currentCountNum,
       submit_to_job_countNum             AS submit_to_job_countNum,
       submit_to_job_currentStayedOver    AS submit_to_job_currentStayedOver,
       submit_to_job_stayedOver           AS submit_to_job_stayedOver,

       submit_to_client_currentCountNum   AS submit_to_client_currentCountNum,
       submit_to_client_countNum          AS submit_to_client_countNum,
       submit_to_client_currentStayedOver AS submit_to_client_currentStayedOver,
       submit_to_client_stayedOver        AS submit_to_client_stayedOver,

       offer_currentCountNum              AS offer_currentCountNum,
       offer_countNum                     AS offer_countNum,
       offer_accept_currentCountNum       AS offer_accept_currentCountNum,
       offer_accept_countNum              AS offer_accept_countNum,

       on_board_currentCountNum           AS on_board_currentCountNum,
       on_board_countNum                  AS on_board_countNum,

       eliminated_currentCountNum         AS eliminated_currentCountNum,
       eliminated_countNum                AS eliminated_countNum,

       interview1                         AS interview1,
       interview2                         AS interview2,
       current_interview1                 AS current_interview1,
       current_interview2                 AS current_interview2,
       two_or_more_interviews             AS two_or_more_interviews,
       current_two_or_more_interviews     AS current_two_or_more_interviews,
       interview_final                    AS interview_final,
       current_interview_final            AS current_interview_final,
       interview_total                    AS interview_total,
       current_interview_total            AS current_interview_total,
       unique_interview_talents           AS unique_interview_talents,
       reserve_interview_total            AS reserve_interview_total,
       reserve_current_interview_total    AS reserve_current_interview_total

FROM ((
          -- submit_to_job
          SELECT tenant_id,
                 company_id,
                 job_id,

                 user_id,
                 user_name,

                 user_role,
                 team_id,
                 team_name,
                 submit_job_created_date       AS add_date,
                 submit_job_created_date       AS event_date,
                 COUNT(DISTINCT CASE
                                    WHEN (
                                        node_status = 1
                                            AND node_type = 10
                                        ) THEN submit_job_id
                     END)                      AS submit_to_job_currentCountNum,
                 COUNT(DISTINCT submit_job_id) AS submit_to_job_countNum,
                 COUNT(DISTINCT IF(
                         node_status = 1 AND node_type = 10 AND
                         TIMESTAMPDIFF(HOUR, submit_job_last_modify_date, NOW()) > 24,
                         submit_job_id, NULL)) AS submit_to_job_currentStayedOver,
                 COUNT(DISTINCT IF(
                         TIMESTAMPDIFF(HOUR, submit_job_last_modify_date, NOW()) > 24,
                         submit_job_id, NULL)) AS submit_to_job_stayedOver,
                 0                             AS submit_to_client_currentCountNum,
                 0                             AS submit_to_client_countNum,
                 0                             AS submit_to_client_currentStayedOver,
                 0                             AS submit_to_client_stayedOver,
                 0                             AS offer_currentCountNum,
                 0                             AS offer_countNum,
                 0                             AS offer_accept_currentCountNum,
                 0                             AS offer_accept_countNum,
                 0                             AS on_board_currentCountNum,
                 0                             AS on_board_countNum,
                 0                             AS eliminated_currentCountNum,
                 0                             AS eliminated_countNum,
                 0                             AS interview1,
                 0                             AS interview2,
                 0                             AS current_interview1,
                 0                             AS current_interview2,
                 0                             AS two_or_more_interviews,
                 0                             AS current_two_or_more_interviews,
                 0                             AS interview_final,
                 0                             AS current_interview_final,
                 0                             AS interview_total,
                 0                             AS current_interview_total,
                 0                             AS unique_interview_talents,
                 0                             AS reserve_interview_total,
                 0                             AS reserve_current_interview_total
          FROM view_application AS application
          GROUP BY tenant_id,
                   company_id,
                   job_id,
                   user_id,
                   user_name,
                   user_role,
                   team_id,
                   team_name,
                   add_date,
                   event_date)
      UNION ALL
      (SELECT tenant_id,
              company_id,
              job_id,

              user_id,
              user_name,

              user_role,
              team_id,
              team_name,
              submit_client_created_date       AS add_date,
              submit_client_time               AS event_date,
              0                                AS submit_to_job_currentCountNum,
              0                                AS submit_to_job_countNum,
              0                                AS submit_to_job_currentStayedOver,
              0                                AS submit_to_job_stayedOver,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     node_status = 1
                                         AND node_type = 20
                                     ) THEN submit_client_id
                  END)                         AS submit_to_client_currentCountNum,
              COUNT(DISTINCT submit_client_id) AS submit_to_client_countNum,
              COUNT(DISTINCT IF(
                      node_status = 1 AND node_type = 10 AND
                      TIMESTAMPDIFF(HOUR, submit_client_last_modified_date, NOW()) > 24,
                      submit_client_id, NULL)) AS submit_to_client_currentStayedOver,
              COUNT(DISTINCT IF(
                      TIMESTAMPDIFF(HOUR, submit_client_last_modified_date, NOW()) > 24,
                      submit_client_id, NULL)) AS submit_to_client_stayedOver,
              0                                AS offer_currentCountNum,
              0                                AS offer_countNum,
              0                                AS offer_accept_currentCountNum,
              0                                AS offer_accept_countNum,
              0                                AS on_board_currentCountNum,
              0                                AS on_board_countNum,
              0                                AS eliminated_currentCountNum,
              0                                AS eliminated_countNum,
              0                                AS interview1,
              0                                AS interview2,
              0                                AS current_interview1,
              0                                AS current_interview2,
              0                                AS two_or_more_interviews,
              0                                AS current_two_or_more_interviews,
              0                                AS interview_final,
              0                                AS current_interview_final,
              0                                AS interview_total,
              0                                AS current_interview_total,
              0                                AS unique_interview_talents,
              0                                AS reserve_interview_total,
              0                                AS reserve_current_interview_total
       FROM view_application AS application
       GROUP BY tenant_id,
                company_id,
                job_id,
                user_id,
                user_name,
                user_role,
                team_id,
                team_name,
                add_date,
                event_date)
      UNION ALL
      (SELECT tenant_id,
              company_id,
              job_id,

              user_id,
              user_name,

              user_role,
              team_id,
              team_name,
              interview_created_date       AS add_date,
              interview_from_time_utc      AS event_date,
              0                            AS submit_to_job_currentCountNum,
              0                            AS submit_to_job_countNum,
              0                            AS submit_to_job_currentStayedOver,
              0                            AS submit_to_job_stayedOver,
              0                            AS submit_to_client_currentCountNum,
              0                            AS submit_to_client_countNum,
              0                            AS submit_to_client_currentStayedOver,
              0                            AS submit_to_client_stayedOver,
              0                            AS offer_currentCountNum,
              0                            AS offer_countNum,
              0                            AS offer_accept_currentCountNum,
              0                            AS offer_accept_countNum,
              0                            AS on_board_currentCountNum,
              0                            AS on_board_countNum,
              0                            AS eliminated_currentCountNum,
              0                            AS eliminated_countNum,
              COUNT(DISTINCT CASE
                                 WHEN interview_progress = 1 THEN interview_id
                  END)                     AS interview1,
              COUNT(DISTINCT CASE
                                 WHEN interview_progress = 2 THEN interview_id
                  END)                     AS interview2,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     interview_progress = 1
                                         AND node_status = 1
                                         AND node_type = 30
                                         AND max_progress_subquery.progress = 1
                                     ) THEN interview_id
                  END)                     AS current_interview1,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     interview_progress = 2
                                         AND node_status = 1
                                         AND node_type = 30
                                         AND max_progress_subquery.progress = 2
                                     ) THEN interview_id
                  END)                     AS current_interview2,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     interview_progress > 2
                                         AND interview_final_round <> 1
                                     ) THEN interview_id
                  END)                     AS two_or_more_interviews,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     interview_progress > 2
                                         AND interview_final_round <> 1
                                         AND node_status = 1
                                         AND node_type = 30
                                     ) THEN interview_id
                  END)                     AS current_two_or_more_interviews,
              COUNT(DISTINCT CASE
                                 WHEN interview_final_round = 1 THEN interview_id
                  END)                     AS interview_final,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     interview_final_round = 1
                                         AND node_status = 1
                                         AND node_type = 30
                                     ) THEN interview_id
                  END)                     AS current_interview_final,
              COUNT(DISTINCT interview_id) AS interview_total,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     node_status = 1
                                         AND node_type = 30
                                     ) THEN interview_id
                  END)                     AS current_interview_total,
              0                            AS unique_interview_talents,
              0                            AS reserve_interview_total,
              0                            AS reserve_current_interview_total
       FROM view_application AS application
                LEFT OUTER JOIN (SELECT application.talent_recruitment_process_id AS application_id,
                                        max(interview_progress)                   AS progress
                                 FROM view_application AS application
                                 GROUP BY application_id) AS max_progress_subquery
                                ON application_id = talent_recruitment_process_id
       GROUP BY tenant_id,
                company_id,
                job_id,
                user_id,
                user_name,
                user_role,
                team_id,
                team_name,
                add_date,
                event_date)
      UNION ALL
      (SELECT tenant_id,
              company_id,
              job_id,

              user_id,
              user_name,

              user_role,
              team_id,
              team_name,
              interview_created_date       AS add_date,
              interview_created_date       AS event_date,
              0                            AS submit_to_job_currentCountNum,
              0                            AS submit_to_job_countNum,
              0                            AS submit_to_job_currentStayedOver,
              0                            AS submit_to_job_stayedOver,
              0                            AS submit_to_client_currentCountNum,
              0                            AS submit_to_client_countNum,
              0                            AS submit_to_client_currentStayedOver,
              0                            AS submit_to_client_stayedOver,
              0                            AS offer_currentCountNum,
              0                            AS offer_countNum,
              0                            AS offer_accept_currentCountNum,
              0                            AS offer_accept_countNum,
              0                            AS on_board_currentCountNum,
              0                            AS on_board_countNum,
              0                            AS eliminated_currentCountNum,
              0                            AS eliminated_countNum,
              0                            AS interview1,
              0                            AS interview2,
              0                            AS current_interview1,
              0                            AS current_interview2,
              0                            AS two_or_more_interviews,
              0                            AS current_two_or_more_interviews,
              0                            AS interview_final,
              0                            AS current_interview_final,
              0                            AS interview_total,
              0                            AS current_interview_total,
              0                            AS unique_interview_talents,
              COUNT(DISTINCT interview_id) AS reserve_interview_total,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     node_status = 1
                                         AND node_type = 30
                                     ) THEN interview_id
                  END)                     AS reserve_current_interview_total
       FROM view_application AS application
       GROUP BY tenant_id,
                company_id,
                job_id,
                user_id,
                user_name,
                user_role,
                team_id,
                team_name,
                add_date,
                event_date)
      UNION ALL
      (SELECT tenant_id,
              company_id,
              job_id,

              user_id,
              user_name,

              user_role,
              team_id,
              team_name,
              offer_created_date       AS add_date,
              offer_created_date       AS event_date,
              0                        AS submit_to_job_currentCountNum,
              0                        AS submit_to_job_countNum,
              0                        AS submit_to_job_currentStayedOver,
              0                        AS submit_to_job_stayedOver,
              0                        AS submit_to_client_currentCountNum,
              0                        AS submit_to_client_countNum,
              0                        AS submit_to_client_currentStayedOver,
              0                        AS submit_to_client_stayedOver,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     node_status = 1
                                         AND node_type = 40
                                     ) THEN offer_id
                  END)                 AS offer_currentCountNum,
              COUNT(DISTINCT offer_id) AS offer_countNum,
              0                        AS offer_accept_currentCountNum,
              0                        AS offer_accept_countNum,
              0                        AS on_board_currentCountNum,
              0                        AS on_board_countNum,
              0                        AS eliminated_currentCountNum,
              0                        AS eliminated_countNum,
              0                        AS interview1,
              0                        AS interview2,
              0                        AS current_interview1,
              0                        AS current_interview2,
              0                        AS two_or_more_interviews,
              0                        AS current_two_or_more_interviews,
              0                        AS interview_final,
              0                        AS current_interview_final,
              0                        AS interview_total,
              0                        AS current_interview_total,
              0                        AS unique_interview_talents,
              0                        AS reserve_interview_total,
              0                        AS reserve_current_interview_total
       FROM view_application AS application
       GROUP BY tenant_id,
                company_id,
                job_id,
                user_id,
                user_name,
                user_role,
                team_id,
                team_name,
                add_date,
                event_date)
      UNION ALL
      (SELECT tenant_id,
              company_id,
              job_id,

              user_id,
              user_name,

              user_role,
              team_id,
              team_name,
              offer_accept_created_date       AS add_date,
              offer_accept_created_date       AS event_date,
              0                               AS submit_to_job_currentCountNum,
              0                               AS submit_to_job_countNum,
              0                               AS submit_to_job_currentStayedOver,
              0                               AS submit_to_job_stayedOver,
              0                               AS submit_to_client_currentCountNum,
              0                               AS submit_to_client_countNum,
              0                               AS submit_to_client_currentStayedOver,
              0                               AS submit_to_client_stayedOver,
              0                               AS offer_currentCountNum,
              0                               AS offer_countNum,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     node_status = 1
                                         AND node_type = 41
                                     ) THEN offer_accept_id
                  END)                        AS offer_accept_currentCountNum,
              COUNT(DISTINCT offer_accept_id) AS offer_accept_countNum,
              0                               AS on_board_currentCountNum,
              0                               AS on_board_countNum,
              0                               AS eliminated_currentCountNum,
              0                               AS eliminated_countNum,
              0                               AS interview1,
              0                               AS interview2,
              0                               AS current_interview1,
              0                               AS current_interview2,
              0                               AS two_or_more_interviews,
              0                               AS current_two_or_more_interviews,
              0                               AS interview_final,
              0                               AS current_interview_final,
              0                               AS interview_total,
              0                               AS current_interview_total,
              0                               AS unique_interview_talents,
              0                               AS reserve_interview_total,
              0                               AS reserve_current_interview_total
       FROM view_application AS application
       GROUP BY tenant_id,
                company_id,
                job_id,
                user_id,
                user_name,
                user_role,
                team_id,
                team_name,
                add_date,
                event_date)
      UNION ALL
      (SELECT tenant_id,
              company_id,
              job_id,

              user_id,
              user_name,

              user_role,
              team_id,
              team_name,
              onboard_node_created_date  AS add_date,
              onboard_date               AS event_date,
              0                          AS submit_to_job_currentCountNum,
              0                          AS submit_to_job_countNum,
              0                          AS submit_to_job_currentStayedOver,
              0                          AS submit_to_job_stayedOver,
              0                          AS submit_to_client_currentCountNum,
              0                          AS submit_to_client_countNum,
              0                          AS submit_to_client_currentStayedOver,
              0                          AS submit_to_client_stayedOver,
              0                          AS offer_currentCountNum,
              0                          AS offer_countNum,
              0                          AS offer_accept_currentCountNum,
              0                          AS offer_accept_countNum,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     node_status = 1
                                         AND node_type = 60
                                     ) THEN onboard_id
                  END)                   AS on_board_currentCountNum,
              COUNT(DISTINCT onboard_id) AS on_board_countNum,
              0                          AS eliminated_currentCountNum,
              0                          AS eliminated_countNum,
              0                          AS interview1,
              0                          AS interview2,
              0                          AS current_interview1,
              0                          AS current_interview2,
              0                          AS two_or_more_interviews,
              0                          AS current_two_or_more_interviews,
              0                          AS interview_final,
              0                          AS current_interview_final,
              0                          AS interview_total,
              0                          AS current_interview_total,
              0                          AS unique_interview_talents,
              0                          AS reserve_interview_total,
              0                          AS reserve_current_interview_total
       FROM view_application AS application
       GROUP BY tenant_id,
                company_id,
                job_id,
                user_id,
                user_name,
                user_role,
                team_id,
                team_name,
                add_date,
                event_date)
      UNION ALL
      (SELECT tenant_id,
              company_id,
              job_id,
              user_id,
              user_name,
              user_role,
              team_id,
              team_name,
              eliminate_created_date       AS add_date,
              eliminate_created_date       AS event_date,
              0                            AS submit_to_job_currentCountNum,
              0                            AS submit_to_job_countNum,
              0                            AS submit_to_job_currentStayedOver,
              0                            AS submit_to_job_stayedOver,
              0                            AS submit_to_client_currentCountNum,
              0                            AS submit_to_client_countNum,
              0                            AS submit_to_client_currentStayedOver,
              0                            AS submit_to_client_stayedOver,
              0                            AS offer_currentCountNum,
              0                            AS offer_countNum,
              0                            AS offer_accept_currentCountNum,
              0                            AS offer_accept_countNum,
              0                            AS on_board_currentCountNum,
              0                            AS on_board_countNum,
              COUNT(DISTINCT CASE
                                 WHEN (
                                     node_status = 1
                                         AND node_type = -1
                                     ) THEN eliminate_id
                  END)                     AS eliminated_currentCountNum,
              COUNT(DISTINCT eliminate_id) AS eliminated_countNum,
              0                            AS interview1,
              0                            AS interview2,
              0                            AS current_interview1,
              0                            AS current_interview2,
              0                            AS two_or_more_interviews,
              0                            AS current_two_or_more_interviews,
              0                            AS interview_final,
              0                            AS current_interview_final,
              0                            AS interview_total,
              0                            AS current_interview_total,
              0                            AS unique_interview_talents,
              0                            AS reserve_interview_total,
              0                            AS reserve_current_interview_total
       FROM view_application AS application
       GROUP BY tenant_id,
                company_id,
                job_id,
                user_id,
                user_name,
                user_role,
                team_id,
                team_name,
                add_date,
                event_date)) AS unified_data;